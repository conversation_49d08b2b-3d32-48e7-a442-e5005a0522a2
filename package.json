{"name": "shadcn-ui-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.7.3"}, "packageManager": "bun@1.2.0", "workspaces": ["apps/*", "packages/*"], "engines": {"node": ">=20"}}