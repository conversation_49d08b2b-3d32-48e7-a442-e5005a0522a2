"use client";

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Icons } from "@workspace/ui/components/icons";

const SocialLoginOptions = () => {
  const t = useTranslations();

  return (
    <div className="grid grid-cols-3 gap-3">
      <Button variant="ghost">
        <Icons.google className="h-4 w-4" /> {t("auth.social.google")}
      </Button>
      <Button variant="ghost">
        <Icons.facebook className="h-4 w-4" /> {t("auth.social.facebook")}
      </Button>
      <Button variant="ghost">
        <Icons.apple className="h-4 w-4" /> {t("auth.social.apple")}
      </Button>
    </div>
  );
};

export default SocialLoginOptions;
