"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import { RHFInput, RHFPasswordInput } from "@workspace/ui/components/rhf";
import {
  createRegisterSchema,
  type RegisterFormData,
} from "@/lib/validations/auth-i18n";

interface RegisterFormProps {
  onSubmit?: (data: RegisterFormData) => Promise<void> | void;
  isLoading?: boolean;
  error?: string | null;
}

export function RegisterForm({
  onSubmit,
  isLoading = false,
  error,
}: RegisterFormProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const t = useTranslations();

  const registerSchema = React.useMemo(() => createRegisterSchema(t), [t]);

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
    },
  });

  const handleSubmit = async (data: RegisterFormData) => {
    if (!onSubmit) return;

    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } catch (error) {
      console.error("Registration error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {error && (
          <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
            {error}
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <RHFInput
            control={form.control}
            name="firstName"
            placeholder={t("auth.register.form.firstNamePlaceholder")}
            disabled={isLoading || isSubmitting}
          />

          <RHFInput
            control={form.control}
            name="lastName"
            placeholder={t("auth.register.form.lastNamePlaceholder")}
            disabled={isLoading || isSubmitting}
          />
        </div>

        <RHFInput
          control={form.control}
          name="email"
          placeholder={t("auth.register.form.emailPlaceholder")}
          type="email"
          disabled={isLoading || isSubmitting}
        />

        <RHFPasswordInput
          control={form.control}
          name="password"
          placeholder={t("auth.register.form.passwordPlaceholder")}
          disabled={isLoading || isSubmitting}
        />

        <RHFPasswordInput
          control={form.control}
          name="confirmPassword"
          placeholder={t("auth.register.form.confirmPasswordPlaceholder")}
          disabled={isLoading || isSubmitting}
        />

        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={isLoading || isSubmitting}
        >
          {isLoading || isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("auth.register.form.submittingButton")}
            </>
          ) : (
            t("auth.register.form.submitButton")
          )}
        </Button>
      </form>
    </Form>
  );
}
