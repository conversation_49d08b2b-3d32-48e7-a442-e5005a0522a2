import { But<PERSON> } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { RegisterForm } from "../../../../features/auth/components/register-form";
import SocialLoginOptions from "../../../../features/auth/components/social-login-options";

type Props = {
  params: Promise<{ locale: string }>;
};

export default async function RegisterPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations();
  return (
    <>
      {/* Sign In Button */}
      <div className="absolute top-0 right-0 p-5">
        <Link href={`/${locale}/auth/login`}>
          <Button variant="ghost">{t("auth.register.signInButton")}</Button>
        </Link>
      </div>

      <div className="w-full max-w-md space-y-6">
        {/* Title */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-center">
            {t("auth.register.title")}
          </h1>
          <p className="text-lg text-center text-muted-foreground">
            {t("auth.register.subtitle")}
          </p>
        </div>

        {/* Social Login Options */}
        <SocialLoginOptions />

        <div className="relative flex items-center">
          <Separator className="flex-1 " />
          <span className="mx-4 font-bold uppercase">
            {t("auth.register.or")}
          </span>
          <Separator className="flex-1" />
        </div>

        {/* Register Form */}
        <RegisterForm />
      </div>
    </>
  );
}
