import { LanguageSwitcher } from "@/components/language-switcher";
import { ThemeSwitcher } from "@/components/theme-swicher";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex h-screen w-full">
      {/* Left image */}
      {/* <div
        className="hidden md:block w-1/2 h-screen bg-no-repeat bg-cover bg-center relative"
        style={{
          backgroundImage: "url('/images/minimal-home-desk-design.jpg')",
        }}
      > */}
      {/* Quotes */}
      {/* Logo */}
      {/* </div> */}

      {/* Right form */}
      <div className="w-full h-screen">
        <div className="p-5 relative w-full flex items-center justify-center h-screen">
          <div className="absolute top-0 left-0 p-5 flex items-center space-x-2">
            <LanguageSwitcher />
            <ThemeSwitcher />
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
