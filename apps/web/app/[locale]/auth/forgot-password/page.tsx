import { But<PERSON> } from "@workspace/ui/components/button";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { ForgotPasswordForm } from "../../../../features/auth/components";

type Props = {
  params: Promise<{ locale: string }>;
};

export default async function ForgotPasswordPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations();
  return (
    <>
      {/* Sign In Button */}
      <div className="absolute top-0 right-0 p-5">
        <Link href={`/${locale}/auth/login`}>
          <Button variant="ghost">
            {t("auth.forgotPassword.signInButton")}
          </Button>
        </Link>
      </div>

      <div className="w-full max-w-md space-y-6">
        {/* Title */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-center">
            {t("auth.forgotPassword.title")}
          </h1>
          <p className="text-lg text-center text-muted-foreground">
            {t("auth.forgotPassword.subtitle")}
          </p>
        </div>

        {/* Forgot Password Form */}
        <ForgotPasswordForm />
      </div>
    </>
  );
}
