import { But<PERSON> } from "@workspace/ui/components/button";
import { Separator } from "@workspace/ui/components/separator";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { LoginForm } from "../../../../features/auth/components";
import SocialLoginOptions from "../../../../features/auth/components/social-login-options";

type Props = {
  params: Promise<{ locale: string }>;
};

export default async function LoginPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations();
  return (
    <>
      {/* Sign Up Button */}
      <div className="absolute top-0 right-0 p-5">
        <Link href={`/${locale}/auth/register`}>
          <Button variant="ghost">{t("auth.login.signUpButton")}</Button>
        </Link>
      </div>

      <div className="w-full max-w-md space-y-6">
        {/* Title */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-center">
            {t("auth.login.title")}
          </h1>
          <p className="text-lg text-center text-muted-foreground">
            {t("auth.login.subtitle")}
          </p>
        </div>

        {/* Social Login Options */}
        <SocialLoginOptions />

        <div className="relative flex items-center">
          <Separator className="flex-1 " />
          <span className="mx-4 font-bold uppercase">{t("auth.login.or")}</span>
          <Separator className="flex-1" />
        </div>

        {/* Login Form */}
        <LoginForm />

        {/* Navigation Links */}
        <div className="text-center space-y-2">
          <div>
            <Link href={`/${locale}/auth/forgot-password`}>
              <Button variant="link">{t("auth.login.forgotPassword")}</Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
