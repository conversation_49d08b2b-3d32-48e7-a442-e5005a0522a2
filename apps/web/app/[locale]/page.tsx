import Link from "next/link";
import { Button } from "@workspace/ui/components/button";

export default function Page() {
  return (
    <div className="flex items-center justify-center min-h-svh">
      <div className="flex flex-col items-center justify-center gap-6 max-w-2xl mx-auto p-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">EdTech Frontend</h1>
          <p className="text-muted-foreground">
            A modern educational platform with authentication components
          </p>
        </div>

        {/* Authentication Components */}
        <div className="w-full space-y-4">
          <h2 className="text-xl font-semibold text-center">
            Authentication Components
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button asChild variant="primary" className="w-full">
              <Link href="/auth/login">Login Page</Link>
            </Button>
            <Button asChild variant="secondary" className="w-full">
              <Link href="/examples/login">Login Examples</Link>
            </Button>
          </div>
        </div>

        {/* Button Examples */}
        <div className="w-full space-y-4">
          <h2 className="text-xl font-semibold text-center">Button Variants</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            <Button>Default</Button>
            <Button variant="primary">Primary</Button>
            <Button variant="primaryOutline">Primary Outline</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="secondaryOutline">Secondary Outline</Button>
            <Button variant="destructive">Danger</Button>
            <Button variant="destructiveOutline">Danger Outline</Button>
            <Button variant="super">Super</Button>
            <Button variant="superOutline">Super Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="sidebar">Sidebar</Button>
            <Button variant="sidebarOutline">Sidebar Outline</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
