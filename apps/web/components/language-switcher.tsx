"use client";

import { routing } from "@/i18n/routing";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useLocale, useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";

export function LanguageSwitcher() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (newLocale: string) => {
    // Remove the current locale from the pathname
    const pathWithoutLocale = pathname.replace(`/${locale}`, "");
    // Navigate to the new locale
    router.push(`/${newLocale}${pathWithoutLocale}`);
  };

  const getLanguageLabel = (localeCode: string) => {
    switch (localeCode) {
      case "en":
        return t("auth.language.english");
      case "vi":
        return t("auth.language.vietnamese");
      default:
        return localeCode;
    }
  };

  const getLanguageIcon = (localeCode: string) => {
    switch (localeCode) {
      case "en":
        return <span>🇺🇸</span>;
      case "vi":
        return <span>🇻🇳</span>;
      default:
        return null;
    }
  };

  return (
    <div className="flex gap-2">
      <Select onValueChange={handleLanguageChange} value={locale}>
        <SelectTrigger className="w-40">
          <SelectValue placeholder={t("auth.language.label")}>
            {getLanguageIcon(locale)}
            {getLanguageLabel(locale)}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="border-2 shadow-none">
          {routing.locales.map((localeOption) => (
            <SelectItem key={localeOption} value={localeOption}>
              {getLanguageIcon(localeOption)}
              {getLanguageLabel(localeOption)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
