import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

// Extended axios request config type
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
}

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api/v1",
  timeout: 15000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Store for queued requests
let isRefreshing = false;
const failedQueue: {
  resolve: (value?: unknown) => void;
  reject: (error?: unknown) => void;
  config: ExtendedAxiosRequestConfig;
}[] = [];

// Logout function to centralize logout logic
const performLogout = () => {
  localStorage.removeItem("accessToken");
  localStorage.removeItem("refreshToken");
  localStorage.removeItem("currentTenantId");

  // Redirect to login page
  if (typeof window !== "undefined") {
    window.location.href = "/auth/login";
  }
};

const processQueue = (
  error: AxiosError | null,
  token: string | null = null
): void => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      if (token) {
        prom.config.headers.Authorization = `Bearer ${token}`;
      }
      prom.resolve(axiosInstance(prom.config));
    }
  });
  failedQueue.length = 0; // Clear the queue
};

// Request interceptor to add auth token and language header
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

const excludedPaths = [
  "/auth/login",
  "/auth/register",
  "/auth/verify-email",
  "/auth/resend-verification",
  "/auth/request-password-reset",
  "/auth/reset-password",
  "/auth/google",
  "/auth/refresh",
] as const;

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedAxiosRequestConfig;
    const currentUrl = originalRequest.url;

    // If error is 401 and we're not already refreshing and not an excluded path
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      currentUrl &&
      !excludedPaths.includes(currentUrl as (typeof excludedPaths)[number])
    ) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject, config: originalRequest });
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = localStorage.getItem("refreshToken");
        if (!refreshToken) {
          throw new Error("No refresh token available");
        }

        const response = await axiosInstance.post<AuthResponse>(
          "/auth/refresh",
          {
            refreshToken,
          }
        );

        const { accessToken, refreshToken: newRefreshToken } = response.data;

        if (!accessToken) {
          throw new Error("Invalid access token received");
        }

        localStorage.setItem("accessToken", accessToken);
        if (newRefreshToken) {
          localStorage.setItem("refreshToken", newRefreshToken);
        }

        axiosInstance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;

        processQueue(null, accessToken);
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);
        processQueue(error, null);
        performLogout();

        throw new AxiosError(
          "Session expired. Please log in again.",
          "AUTHENTICATION_FAILED",
          originalRequest,
          null,
          {
            ...error.response,
            status: 401,
            statusText: "Unauthorized - Session Expired",
          }
        );
      } finally {
        isRefreshing = false;
      }
    }

    // Handle refresh token endpoint 401
    if (error.response?.status === 401 && currentUrl === "/auth/refresh") {
      console.error("Refresh token is invalid or expired");
      performLogout();
      return Promise.reject(error);
    }

    // Handle other errors
    if (error.response?.status === 403) {
      console.error("Access forbidden");
    } else if (error.response?.status && error.response.status >= 500) {
      console.error("Server error:", error.response.data);
    }

    return Promise.reject(error);
  }
);

// Function to update language header when language changes
export const updateLanguageHeader = (language: string): void => {
  axiosInstance.defaults.headers.lang = language;
};

// Function to update tenant header when tenant changes
export const updateTenantHeader = (tenantId: string): void => {
  axiosInstance.defaults.headers["x-tenant-id"] = tenantId;
  localStorage.setItem("currentTenantId", tenantId);
};

// Function to manually logout (can be called from components)
export const logout = (): void => {
  performLogout();
};

// API response types
export interface ApiResponse<T = unknown> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
}

// Helper function to handle API errors
export const handleApiError = (error: unknown): ApiError => {
  if (error && typeof error === "object" && "response" in error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as ApiError;
    }
    return {
      message: axiosError.message || "An unexpected error occurred",
      status: axiosError.response?.status,
    };
  }

  return {
    message:
      error instanceof Error ? error.message : "An unexpected error occurred",
  };
};

// Export the configured axios instance
export default axiosInstance;
