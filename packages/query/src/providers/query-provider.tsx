"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ReactNode } from "react";
import { queryClient } from "../client";

export function QueryProvider({
  children,
  initialIsOpen,
}: {
  children: ReactNode;
  initialIsOpen?: boolean;
}) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={initialIsOpen ?? false} />
    </QueryClientProvider>
  );
}
